import { Facility } from "src/hooks/useFacilities";
import { <PERSON>alog, Button, YStack, Text, XStack, TextArea } from "tamagui";
import FacilityDrawer from "./FacilityDrawer";
import PatientSearchDrawer from "./PatientSearchDrawer";
import { useState } from "react";
import { PatientInfo } from "@/nurse/requestVisit";
import { Platform } from "react-native";
import { usePatients } from "src/hooks/usePatientsByFacilities";
import { useAddpatientDialogStyle } from "./componentstyles/AddPatientDialogStyles";
import { Plus } from "@tamagui/lucide-icons";

interface AddPatientDialogProps {
  open: boolean;
  onClose: (open: boolean) => void;
  facilities: Facility[];
  facilitiesLoading: boolean;
  facilitiesError: any;
  ehrType: string;
  onAddpatient: (patient: PatientInfo) => void;
}

export function AddPatientDialog({
  open,
  onClose,
  facilities,
  facilitiesLoading,
  facilitiesError,
  ehrType,
  onAddpatient,
}: AddPatientDialogProps): JSX.Element {
  const styles = useAddpatientDialogStyle();
  const [patientNameSearch, setPatientNameSearch] = useState<string>("Al");
  const [selectedPatient, setSelectedPatient] = useState<PatientInfo>({
    id: "",
    name: "",
    dob: "",
    facilityId: "",
  });
  const [selectedFacilityId, setSelectedFacilityId] = useState<string>(
    ""
  );
  const [addPatientDisabled, setAddPatientDisabled] = useState<boolean>(true);

  const handleFacilitySelect = (facilityId: string) => {
    setSelectedFacilityId(facilityId);
  };

  const {
    data: patients,
    isLoading: patientsLoading,
    error: patientsError,
  } = usePatients(selectedFacilityId || "", patientNameSearch);

  const handlePatientSelect = (
    patientId: string,
    patientFirstName: string,
    dob: string
  ) => {
    setSelectedPatient({ id: patientId, name: patientFirstName, dob , facilityId: selectedFacilityId});
    setAddPatientDisabled(false);
  };

  const addPatient = () => {
    onAddpatient(selectedPatient);
    onClose(false);
  };

  return (
    <Dialog modal open={open} onOpenChange={onClose}>
      <Dialog.Overlay {...styles.overlay} />
      <Dialog.Content {...styles.dialogContent}>
        <YStack {...styles.container}>
          <Text {...styles.headerText}>Add a patient to call</Text>

          <YStack {...styles.facilityContainer}>
            <Text {...styles.facilityTitle}>Facility</Text>
            <YStack>
              {facilitiesLoading ? (
                <Text>Loading facilities...</Text>
              ) : facilitiesError ? (
                <Text>Error loading facilities</Text>
              ) : (
                <FacilityDrawer
                  data={facilities || []}
                  placeholder="Select a Facility"
                  onSelect={(id: string) => handleFacilitySelect(id)}
                />
              )}
            </YStack>
          </YStack>

          <YStack {...styles.facilityContainer}>
            {ehrType !== "manual" && (
              <Text {...styles.facilityTitle}>Patient</Text>
            )}
            {ehrType === "manual" ? (
              <YStack width="100%">
                <Text {...styles.facilityTitle}>Patient</Text>
                <TextArea
                  {...styles.patientNameTextArea}
                  placeholder="Type Patient Name"
                  placeholderTextColor="$textcolor"
                  overflow="hidden"
                  value={selectedPatient.name}
                  onChangeText={(text) => {
                    setSelectedPatient((prev) => ({
                      ...prev,
                      name: text,
                      id: text,
                    }));
                  }}
                />
                <Text {...styles.facilityTitle} mt={"$4"}>
                  Date of Birth (MM/DD/YYYY)
                </Text>
                <TextArea
                  {...styles.patientNameTextArea}
                  placeholder="MM-DD-YYYY"
                  placeholderTextColor="$textcolor"
                  keyboardType={
                    Platform.OS === "ios" ? "number-pad" : "numeric"
                  }
                  maxLength={10}
                  value={selectedPatient.dob}
                  onChangeText={(text) => {
                    const dob = formatDOB(text);
                    setSelectedPatient((prev) => ({
                      ...prev,
                      dob,
                    }));
                  }}
                />
              </YStack>
            ) : (
              <YStack marginBlockEnd={50}>
                <PatientSearchDrawer
                  data={patients || []}
                  placeholder="Select a Patient"
                  onSelect={(id: string, firstName: string, dob: string) =>
                    handlePatientSelect(id, firstName, dob)
                  }
                  onSearch={(query: string) => {
                    setPatientNameSearch(query);
                    return Promise.resolve();
                  }}
                  disabled={!selectedFacilityId}
                  loading={patientsLoading}
                  error={patientsError?.message || ""}
                />
              </YStack>
            )}
          </YStack>

          <Button
            icon={<Plus size={"$1"} color={"white"} />}
            {...(addPatientDisabled
              ? styles.addPatientBtnDisabled
              : styles.addPatientBtn)}
            disabled={addPatientDisabled}
            onPress={addPatient}
          >
            Add
          </Button>

          <Button {...styles.cancelBtn} onPress={() => onClose(false)}>
            Cancel
          </Button>
        </YStack>
      </Dialog.Content>
    </Dialog>
  );
}

export const formatDOB = (raw: string) => {
  const digits = raw.replace(/\D/g, "").slice(0, 8);
  if (digits.length <= 2) return digits;
  if (digits.length <= 4) {
    return `${digits.slice(0, 2)}-${digits.slice(2)}`;
  }
  return `${digits.slice(0, 2)}-${digits.slice(2, 4)}-${digits.slice(4)}`;
};
