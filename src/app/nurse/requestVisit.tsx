import { useLocal<PERSON>earch<PERSON>ara<PERSON>, useRouter } from "expo-router";
import { <PERSON><PERSON>, <PERSON>ton, Card, Text, View, XStack, YStack } from "tamagui";
import { useAuth } from "~/context/AuthContext";
import { useRequestVisitStyle } from "./Styles/RequestVisitStyles";
import { useEffect, useState } from "react";
import ScreenHeader from "src/components/ScreenHeader";
import SheetDemo from "src/components/SettingsDrawer";
import { HorizontalDashedLine } from "src/components/DashedLine";
import Title from "src/components/Title";
import { ArrowRight, Plus } from "@tamagui/lucide-icons";
import SinglePatientRequestVisit from "src/components/SinglePatientRequestVisit";
import MultiPatientRequestVisit from "src/components/MultipatientRequestVisit";
import { ScrollView } from "react-native";
import { AddPatientDialog } from "src/components/AddPatientDialog";
import { useFacilities } from "src/hooks/useFacilities";
import ScanDocumentComponent from "src/components/ScanDocument";
import { SelectDocument } from "src/components/SelectDocuments";

export type PatientInfo = {
  id: string;
  name: string;
  dob: string;
};

export interface AttachmentsInfo {
  fileKeys?: string[];
  fileNames?: string[];
  fileTypes?: string[];
}

export type PatientData = {
  patient: PatientInfo;
  chiefComplaint: string;
  telehealthConsentFiles: AttachmentsInfo[];
};

export default function RequestVisit() {
  const isSinglePatient = useLocalSearchParams();
  const [isSinglePatinetVisit, setIsSinglePatinetVisit] = useState(false);
  const { localUser, signOut } = useAuth();
  const ehrType = localUser?.ehr || "";
  const styles = useRequestVisitStyle();
  const [selectedPatients, setSelectedPatients] = useState<PatientInfo[]>([]);
  const [openSettingsDrawer, setOpenSettingsDrawer] = useState(false);
  const router = useRouter();
  const [profileImage, setProfileImage] = useState<string>("");
  const [patientConsentDisabled, setPatientConsentDisabled] = useState(true);
  const [addPatientDialogOpen, setAddPatientDialogOpen] = useState(false);
  const [chiefComplaint, setChiefComplaint] = useState<string>("");
  const [scannedImages, setScannedImages] = useState<string[]>([]);
  const [showUploader, setShowUploader] = useState(false);
  const [showScanDocument, setShowScanDocument] = useState(false);

  const {
    data: facilities,
    isLoading: facilitiesLoading,
    error: facilitiesError,
  } = useFacilities();
  const initials = localUser
    ? localUser?.firstName?.charAt(0) + localUser?.lastName?.charAt(0)
    : "User";
  useEffect(() => {
    if (
      localUser?.profilePicUrl !== null &&
      localUser?.profilePicUrl !== undefined &&
      localUser?.profilePicUrl !== ""
    ) {
      setProfileImage(localUser?.profilePicUrl);
    }
  }, [localUser?.profilePicUrl]);

  const openSettings = () => {
    setOpenSettingsDrawer(true);
  };
  const navigateBack = () => {
    router.back();
  };
  useEffect(() => {
    setIsSinglePatinetVisit(isSinglePatient.isSinglePatient === "1");
  }, [isSinglePatient]);

  const onAddpatient = (patient: PatientInfo) => {
    setSelectedPatients((prev) => [...prev, patient]);
  };

  useEffect(() => {
    isSinglePatinetVisit
      ? setPatientConsentDisabled(
          !(selectedPatients?.length > 0 && chiefComplaint?.length > 0)
        )
      : setPatientConsentDisabled(!(selectedPatients?.length > 0));
  }, [selectedPatients, chiefComplaint, isSinglePatinetVisit]);

  const handleAddDocument = () => {
    if (scannedImages.length >= 3) {
      return;
    }
    setShowUploader(true);
  };

  const handleRemoveImage = (url: string) => {
    setScannedImages((prev) => prev.filter((u) => u !== url));
  };

  const handleCloseScanDocument = (images: string[]) => {
    setShowScanDocument(false);
    setScannedImages(images);
  };

  const handleOpenScanDocument = () => {
    setShowUploader(false);
    setShowScanDocument(true);
  };

  const handleAddGalleryImage = (uri: string) => {
    setScannedImages((prev) => [...prev, uri]);
  };

  const onPatientConsent = () => {
    if (isSinglePatinetVisit) {
      const patientData: PatientData[] = selectedPatients.map((patient) => ({
        patient,
        chiefComplaint,
        telehealthConsentFiles: [],
      }));
      console.log(patientData);
      console.log("----------------");
      console.log(selectedPatients);
      router.push({
        pathname: "/nurse/telehealthconsent",
        params: {
          patientData: JSON.stringify(patientData),
          selectedPatients: JSON.stringify(selectedPatients),
          isSinglePatient: 1,
        },
      });
    } else {
      const patientData: PatientData[] = selectedPatients.map((patient) => ({
        patient,
        chiefComplaint: "",
        telehealthConsentFiles: [],
      }));

      router.push({
        pathname: "/nurse/patient-consent/[index]",
        params: {
          index: "0",
          patientData: JSON.stringify(patientData),
          selectedPatients: JSON.stringify(selectedPatients),
        },
      });
    }
  };

  return (
    <View {...styles.container}>
      <YStack {...styles.mainStack}>
        <ScreenHeader
          onAvatarPress={openSettings}
          screenName={
            isSinglePatinetVisit
              ? "Request Single call visit"
              : "Request Multi-call visit"
          }
          onBackPress={navigateBack}
          shouldShowBackButton={false}
        />

        {isSinglePatinetVisit && (
          <YStack>
            <Card {...styles.profileInfoCard}>
              <XStack {...styles.profileInfoCardContainer}>
                <XStack>
                  <Avatar {...styles.avatarContainer}>
                    <Avatar.Image source={{ uri: profileImage }} />
                    <Avatar.Fallback {...styles.avatarFallBack}>
                      <Text color="white">{initials}</Text>
                    </Avatar.Fallback>
                  </Avatar>

                  <YStack marginInlineStart={10} gap="$1">
                    <Text {...styles.nameText}>
                      {localUser?.firstName + " " + localUser?.lastName}
                    </Text>
                    <Text {...styles.emailText}>{localUser?.email}</Text>
                    <YStack maxW={60} marginBlockStart={5}>
                      <Title
                        text="Nurse"
                        backgroundColor="$nurseBadgeBackgroundColor"
                        borderColor="$primaryBorderColor"
                      />
                    </YStack>
                  </YStack>
                </XStack>

                <Text {...styles.signOutText} onPress={signOut}>
                  Sign out
                </Text>
              </XStack>
            </Card>

            <View marginBlock={15}>
              <HorizontalDashedLine />
            </View>
          </YStack>
        )}

        {!isSinglePatinetVisit && selectedPatients.length > 0 && (
          <View>
            <Button
              icon={<Plus size={"$1"} />}
              {...styles.addPatientBtn}
              onPress={() => setAddPatientDialogOpen(true)}
            >
              Add a patient
            </Button>
            <Text {...styles.patientsText}>
              PATIENTS ({selectedPatients?.length})
            </Text>
          </View>
        )}
        <View {...styles.requestVisitContainer}>
          <ScrollView
            contentContainerStyle={{
              flexGrow: 1,
              paddingBottom: 5,
            }}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          >
            {isSinglePatinetVisit ? (
              <SinglePatientRequestVisit
                selectedPatients={selectedPatients}
                setselectedPatients={setSelectedPatients}
                facilities={facilities || []}
                facilitiesLoading={facilitiesLoading}
                facilitiesError={facilitiesError}
                ehrType={ehrType}
                chiefComplaint={chiefComplaint}
                setChiefComplaint={setChiefComplaint}
                scannedImages={scannedImages}
                handleAddDocument={handleAddDocument}
                handleRemoveImage={handleRemoveImage}
              />
            ) : (
              <MultiPatientRequestVisit
                onAddpatient={() => setAddPatientDialogOpen(true)}
                selectedPatients={selectedPatients}
              />
            )}
          </ScrollView>
        </View>
      </YStack>
      <YStack marginInline={20} marginBlockEnd={20} gap={"$3"}>
        <Button
          iconAfter={<ArrowRight size={"$1"} color={"white"} />}
          {...(patientConsentDisabled
            ? styles.patientConsentBtnDisabled
            : styles.patientConsentBtn)}
          disabled={patientConsentDisabled}
          onPress={onPatientConsent}
        >
          Patient consent
        </Button>
        <Button {...styles.cancelBtn} onPress={navigateBack}>
          Cancel
        </Button>
      </YStack>

      {openSettingsDrawer && (
        <SheetDemo open={openSettingsDrawer} setOpen={setOpenSettingsDrawer} />
      )}
      {addPatientDialogOpen && (
        <AddPatientDialog
          open={addPatientDialogOpen}
          onClose={setAddPatientDialogOpen}
          facilities={facilities || []}
          facilitiesLoading={facilitiesLoading}
          facilitiesError={facilitiesError}
          ehrType={ehrType}
          onAddpatient={onAddpatient}
        />
      )}

      {showScanDocument && (
        <View
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 9999,
            backgroundColor: "#fff",
          }}
        >
          <ScanDocumentComponent
            open={showScanDocument}
            initialImages={scannedImages}
            onClose={handleCloseScanDocument}
            maxImages={3}
          />
        </View>
      )}
      {!showScanDocument && showUploader && (
        <SelectDocument
          open={showUploader}
          onClose={setShowUploader}
          onOpenScanDocument={handleOpenScanDocument}
          onAddImage={handleAddGalleryImage}
          scannedImages={scannedImages}
        />
      )}
    </View>
  );
}
